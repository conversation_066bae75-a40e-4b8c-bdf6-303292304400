<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Searchable Dropdown</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #007bff;
        }

        body {
            padding: 2rem;
            background-color: #f8f9fa;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 0.5rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }

        /* Searchable Select Dropdown Styles */
        .searchable-select-container {
            position: relative;
        }

        .searchable-select-input {
            cursor: text;
        }

        .searchable-select-input:focus {
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        .dropdown-toggle-btn {
            border-left: none;
            background-color: #f8f9fa;
            transition: all 0.2s ease;
        }

        .dropdown-toggle-btn:hover {
            background-color: #e9ecef;
        }

        .dropdown-toggle-btn:focus {
            box-shadow: none;
            border-color: var(--primary-blue);
        }

        .searchable-select-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            z-index: 1050;
            background: white;
            border: 1px solid #dee2e6;
            border-top: none;
            border-radius: 0 0 0.375rem 0.375rem;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            max-height: 300px;
            overflow-y: auto;
        }

        .searchable-select-dropdown.show {
            display: block !important;
        }

        .dropdown-content {
            padding: 0;
        }

        .dropdown-item {
            padding: 0.5rem 0.75rem;
            cursor: pointer;
            border-bottom: 1px solid #f8f9fa;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            font-size: 0.9rem;
        }

        .dropdown-item:last-child {
            border-bottom: none;
        }

        .dropdown-item:hover {
            background-color: #f8f9fa;
            color: var(--primary-blue);
        }

        .dropdown-item.selected {
            background-color: var(--primary-blue);
            color: white;
        }

        .dropdown-item.selected:hover {
            background-color: #0056b3;
        }

        .dropdown-item.highlighted {
            background-color: #e3f2fd;
            color: var(--primary-blue);
        }

        .dropdown-item i {
            width: 16px;
            text-align: center;
        }

        .no-results {
            padding: 1rem;
            text-align: center;
            color: #6c757d;
            font-style: italic;
        }

        .clear-selection {
            position: absolute;
            right: 45px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #6c757d;
            cursor: pointer;
            padding: 0;
            width: 20px;
            height: 20px;
            display: none;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.2s ease;
        }

        .clear-selection:hover {
            background-color: #f8f9fa;
            color: #dc3545;
        }

        .searchable-select-container.has-value .clear-selection {
            display: flex;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2>Test Searchable Court Instance Dropdown</h2>
        <p class="text-muted">Testează funcționalitatea dropdown-ului pentru instanțe judecătorești</p>
        
        <form>
            <div class="mb-3">
                <label for="institutie" class="form-label">
                    <i class="fas fa-university me-1"></i>
                    Instanță judecătorească:
                </label>
                
                <div class="searchable-select-container position-relative">
                    <select class="d-none" id="institutie" name="institutie">
                        <option value="">-- Toate instanțele --</option>
                        <option value="CurteadeApelBUCURESTI">Curtea de Apel București</option>
                        <option value="CurteadeApelCLUJ">Curtea de Apel Cluj</option>
                        <option value="TribunalulBUCURESTI">Tribunalul București</option>
                        <option value="TribunalulCLUJ">Tribunalul Cluj</option>
                        <option value="JudecatoriaSECTORUL1BUCURESTI">Judecătoria Sectorul 1 București</option>
                        <option value="JudecatoriaCLUJNAPOCA">Judecătoria Cluj-Napoca</option>
                    </select>
                    
                    <div class="input-group">
                        <input type="text" 
                               class="form-control searchable-select-input" 
                               id="institutie-search"
                               placeholder="Căutați sau selectați o instanță..."
                               autocomplete="off">
                        <button class="clear-selection" type="button" aria-label="Șterge selecția" title="Șterge selecția">
                            <i class="fas fa-times"></i>
                        </button>
                        <button class="btn btn-outline-secondary dropdown-toggle-btn" type="button" aria-label="Deschide lista">
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    </div>
                    
                    <div class="searchable-select-dropdown" style="display: none;">
                        <div class="dropdown-content">
                            <div class="dropdown-item" data-value="">
                                <i class="fas fa-list me-2 text-muted"></i>
                                -- Toate instanțele --
                            </div>
                            <div class="dropdown-item" data-value="CurteadeApelBUCURESTI">
                                <i class="fas fa-balance-scale me-2 text-primary"></i>
                                Curtea de Apel București
                            </div>
                            <div class="dropdown-item" data-value="CurteadeApelCLUJ">
                                <i class="fas fa-balance-scale me-2 text-primary"></i>
                                Curtea de Apel Cluj
                            </div>
                            <div class="dropdown-item" data-value="TribunalulBUCURESTI">
                                <i class="fas fa-gavel me-2 text-info"></i>
                                Tribunalul București
                            </div>
                            <div class="dropdown-item" data-value="TribunalulCLUJ">
                                <i class="fas fa-gavel me-2 text-info"></i>
                                Tribunalul Cluj
                            </div>
                            <div class="dropdown-item" data-value="JudecatoriaSECTORUL1BUCURESTI">
                                <i class="fas fa-university me-2 text-secondary"></i>
                                Judecătoria Sectorul 1 București
                            </div>
                            <div class="dropdown-item" data-value="JudecatoriaCLUJNAPOCA">
                                <i class="fas fa-university me-2 text-secondary"></i>
                                Judecătoria Cluj-Napoca
                            </div>
                        </div>
                    </div>
                </div>
                
                <small class="form-text text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    Testează căutarea prin tastare: "București", "Cluj", "Curtea", etc.
                </small>
            </div>
            
            <div class="mb-3">
                <label class="form-label">Valoare selectată:</label>
                <div id="selected-value" class="form-control-plaintext text-muted">Nicio selecție</div>
            </div>
            
            <button type="button" class="btn btn-primary" onclick="showSelectedValue()">
                Afișează valoarea selectată
            </button>
        </form>
    </div>

    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script>
        // Test script to show selected value
        function showSelectedValue() {
            const select = document.getElementById('institutie');
            const display = document.getElementById('selected-value');
            
            if (select.value) {
                display.textContent = `Cod: ${select.value} | Text: ${select.options[select.selectedIndex].text}`;
                display.className = 'form-control-plaintext text-success';
            } else {
                display.textContent = 'Nicio selecție';
                display.className = 'form-control-plaintext text-muted';
            }
        }

        // Monitor changes
        document.getElementById('institutie').addEventListener('change', showSelectedValue);
    </script>
    
    <script>
        /**
         * Initialize Searchable Court Instance Dropdown
         */
        function initSearchableCourtDropdown() {
            const container = document.querySelector('.searchable-select-container');
            const input = document.getElementById('institutie-search');
            const hiddenSelect = document.getElementById('institutie');
            const dropdown = document.querySelector('.searchable-select-dropdown');
            const dropdownContent = document.querySelector('.dropdown-content');
            const toggleBtn = document.querySelector('.dropdown-toggle-btn');
            const clearBtn = document.querySelector('.clear-selection');

            if (!container || !input || !hiddenSelect || !dropdown) {
                console.error('Searchable dropdown elements not found');
                return;
            }

            let allItems = [];
            let filteredItems = [];
            let highlightedIndex = -1;
            let isOpen = false;

            // Initialize items array from dropdown content
            function initializeItems() {
                const items = dropdown.querySelectorAll('.dropdown-item');
                allItems = Array.from(items).map(item => ({
                    element: item,
                    value: item.dataset.value,
                    text: item.textContent.trim(),
                    searchText: removeAccents(item.textContent.trim().toLowerCase())
                }));
                filteredItems = [...allItems];
            }

            // Remove Romanian diacritics for better search
            function removeAccents(str) {
                const accents = {
                    'ă': 'a', 'â': 'a', 'î': 'i', 'ș': 's', 'ț': 't',
                    'Ă': 'A', 'Â': 'A', 'Î': 'I', 'Ș': 'S', 'Ț': 'T'
                };
                return str.replace(/[ăâîșțĂÂÎȘȚ]/g, match => accents[match] || match);
            }

            // Filter items based on search term
            function filterItems(searchTerm) {
                const normalizedTerm = removeAccents(searchTerm.toLowerCase());

                filteredItems = allItems.filter(item => {
                    return item.searchText.includes(normalizedTerm);
                });

                renderFilteredItems();
                highlightedIndex = -1;
            }

            // Render filtered items in dropdown
            function renderFilteredItems() {
                dropdownContent.innerHTML = '';

                if (filteredItems.length === 0) {
                    const noResults = document.createElement('div');
                    noResults.className = 'no-results';
                    noResults.innerHTML = '<i class="fas fa-search me-2"></i>Nu s-au găsit rezultate';
                    dropdownContent.appendChild(noResults);
                    return;
                }

                filteredItems.forEach((item, index) => {
                    const clonedItem = item.element.cloneNode(true);
                    clonedItem.addEventListener('click', () => selectItem(item));
                    dropdownContent.appendChild(clonedItem);
                });
            }

            // Select an item
            function selectItem(item) {
                input.value = item.text;
                hiddenSelect.value = item.value;

                // Update selected state
                allItems.forEach(i => i.element.classList.remove('selected'));
                item.element.classList.add('selected');

                closeDropdown();
                updateClearButtonVisibility();

                // Trigger change event for form handling
                hiddenSelect.dispatchEvent(new Event('change', { bubbles: true }));
            }

            // Open dropdown
            function openDropdown() {
                if (isOpen) return;

                isOpen = true;
                dropdown.classList.add('show');
                toggleBtn.querySelector('i').classList.replace('fa-chevron-down', 'fa-chevron-up');
            }

            // Close dropdown
            function closeDropdown() {
                if (!isOpen) return;

                isOpen = false;
                dropdown.classList.remove('show');
                toggleBtn.querySelector('i').classList.replace('fa-chevron-up', 'fa-chevron-down');
                highlightedIndex = -1;
                removeHighlight();
            }

            // Update clear button visibility
            function updateClearButtonVisibility() {
                if (input.value.trim() && input.value !== '-- Toate instanțele --') {
                    container.classList.add('has-value');
                } else {
                    container.classList.remove('has-value');
                }
            }

            // Highlight item by index
            function highlightItem(index) {
                removeHighlight();

                if (index >= 0 && index < filteredItems.length) {
                    highlightedIndex = index;
                    const items = dropdownContent.querySelectorAll('.dropdown-item');
                    if (items[index]) {
                        items[index].classList.add('highlighted');
                        items[index].scrollIntoView({ block: 'nearest' });
                    }
                }
            }

            // Remove highlight from all items
            function removeHighlight() {
                dropdownContent.querySelectorAll('.dropdown-item').forEach(item => {
                    item.classList.remove('highlighted');
                });
            }

            // Clear selection
            function clearSelection() {
                input.value = '';
                hiddenSelect.value = '';
                allItems.forEach(item => item.element.classList.remove('selected'));
                updateClearButtonVisibility();
                closeDropdown();
                input.focus();

                // Trigger change event
                hiddenSelect.dispatchEvent(new Event('change', { bubbles: true }));
            }

            // Event listeners
            input.addEventListener('input', function() {
                const value = this.value;
                filterItems(value);

                if (value.trim()) {
                    openDropdown();
                } else {
                    closeDropdown();
                }

                updateClearButtonVisibility();
            });

            input.addEventListener('focus', function() {
                if (filteredItems.length > 0) {
                    openDropdown();
                }
            });

            input.addEventListener('keydown', function(e) {
                if (!isOpen) {
                    if (e.key === 'ArrowDown' || e.key === 'Enter') {
                        e.preventDefault();
                        openDropdown();
                        if (filteredItems.length > 0) {
                            highlightItem(0);
                        }
                    }
                    return;
                }

                switch (e.key) {
                    case 'ArrowDown':
                        e.preventDefault();
                        highlightItem(Math.min(highlightedIndex + 1, filteredItems.length - 1));
                        break;

                    case 'ArrowUp':
                        e.preventDefault();
                        highlightItem(Math.max(highlightedIndex - 1, 0));
                        break;

                    case 'Enter':
                        e.preventDefault();
                        if (highlightedIndex >= 0 && filteredItems[highlightedIndex]) {
                            selectItem(filteredItems[highlightedIndex]);
                        }
                        break;

                    case 'Escape':
                        e.preventDefault();
                        closeDropdown();
                        break;
                }
            });

            toggleBtn.addEventListener('click', function(e) {
                e.preventDefault();
                if (isOpen) {
                    closeDropdown();
                } else {
                    filterItems(input.value);
                    openDropdown();
                }
            });

            clearBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                clearSelection();
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!container.contains(e.target)) {
                    closeDropdown();
                }
            });

            // Initialize
            initializeItems();
            updateClearButtonVisibility();

            console.log('Searchable dropdown initialized successfully');
        }

        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            initSearchableCourtDropdown();
        });
    </script>
</body>
</html>
