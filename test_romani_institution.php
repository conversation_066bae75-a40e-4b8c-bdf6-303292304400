<?php
/**
 * Specific test for the JudecatoriaROMANI institution code that was causing SOAP API errors
 * This script tests the complete validation and fallback mechanism for this specific case
 */

require_once 'includes/InstitutionCodeValidator.php';

// Set error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>JudecatoriaROMANI Institution Code Test</h1>\n";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    .success { color: green; }
    .warning { color: orange; }
    .error { color: red; }
    .info { color: blue; }
    .code { font-family: monospace; background-color: #f5f5f5; padding: 2px 4px; }
</style>\n";

echo "<div class='test-section'>\n";
echo "<h2>Original Issue</h2>\n";
echo "<p class='error'>Original error: 'Instance validation error: 'JudecatoriaROMANI' is not a valid value for Institut<PERSON>'</p>\n";
echo "<p class='info'>This test validates that our solution properly handles this specific institution code.</p>\n";
echo "</div>\n";

echo "<div class='test-section'>\n";
echo "<h2>Institution Code Validation Test</h2>\n";

$testCode = 'JudecatoriaROMANI';
echo "<p>Testing institution code: <span class='code'>{$testCode}</span></p>\n";

// Test validation
$validation = InstitutionCodeValidator::validateAndMap($testCode);

echo "<h3>Validation Results:</h3>\n";
echo "<ul>\n";
echo "<li>Original code: <span class='code'>{$testCode}</span></li>\n";
echo "<li>Final code: <span class='code'>{$validation['code']}</span></li>\n";
echo "<li>Was mapped: " . ($validation['mapped'] ? '<span class="warning">YES</span>' : '<span class="success">NO</span>') . "</li>\n";
echo "<li>Fallback needed: " . ($validation['fallback_needed'] ? '<span class="error">YES</span>' : '<span class="success">NO</span>') . "</li>\n";
echo "<li>Is valid code: " . (InstitutionCodeValidator::isValidCode($testCode) ? '<span class="success">YES</span>' : '<span class="error">NO</span>') . "</li>\n";
echo "<li>Is known problematic: " . (InstitutionCodeValidator::isKnownProblematic($testCode) ? '<span class="warning">YES</span>' : '<span class="info">NO</span>') . "</li>\n";

if ($validation['warning']) {
    echo "<li>Warning message: <span class='warning'>{$validation['warning']}</span></li>\n";
}

echo "</ul>\n";

// Test alternative mapping
$alternative = InstitutionCodeValidator::getValidAlternative($testCode);
if ($alternative) {
    echo "<p class='success'>Valid alternative found: <span class='code'>{$alternative}</span></p>\n";
} else {
    echo "<p class='warning'>No valid alternative mapping configured</p>\n";
}

echo "</div>\n";

echo "<div class='test-section'>\n";
echo "<h2>SOAP API Integration Test</h2>\n";

try {
    // Test with DosarService if available
    if (file_exists('src/Services/DosarService.php')) {
        require_once 'src/Services/DosarService.php';
        
        if (class_exists('App\Services\DosarService')) {
            echo "<h3>Testing with DosarService</h3>\n";
            
            $testParams = [
                'institutie' => $testCode,
                'numarDosar' => '',
                'numeParte' => '',
                'obiectDosar' => '',
                'dataStart' => '',
                'dataStop' => '',
                '_maxResults' => 5
            ];
            
            echo "<p class='info'>Attempting search with JudecatoriaROMANI...</p>\n";
            
            try {
                $dosarService = new \App\Services\DosarService();
                $results = $dosarService->cautareAvansata($testParams);
                
                echo "<p class='success'>Search completed without throwing exception!</p>\n";
                
                // Check for institution metadata
                if (is_array($results) && isset($results['_institution_metadata'])) {
                    $metadata = $results['_institution_metadata'];
                    echo "<h4>Institution Validation Metadata:</h4>\n";
                    echo "<ul>\n";
                    echo "<li>Original code: <span class='code'>{$metadata['original_code']}</span></li>\n";
                    echo "<li>Final code used: <span class='code'>{$metadata['final_code']}</span></li>\n";
                    echo "<li>Code was mapped: " . ($metadata['was_mapped'] ? '<span class="warning">YES</span>' : '<span class="success">NO</span>') . "</li>\n";
                    echo "<li>Fallback used: " . ($metadata['fallback_used'] ? '<span class="error">YES</span>' : '<span class="success">NO</span>') . "</li>\n";
                    if ($metadata['warning']) {
                        echo "<li>Warning: <span class='warning'>{$metadata['warning']}</span></li>\n";
                    }
                    echo "</ul>\n";
                    
                    // Remove metadata and show results
                    unset($results['_institution_metadata']);
                    $resultCount = is_array($results) ? count($results) : 0;
                    echo "<p class='info'>Search returned {$resultCount} results</p>\n";
                    
                    if ($metadata['fallback_used']) {
                        echo "<p class='success'>✓ Fallback mechanism worked correctly - search completed without institution filter</p>\n";
                    } elseif ($metadata['was_mapped']) {
                        echo "<p class='success'>✓ Code mapping worked correctly - search used alternative institution code</p>\n";
                    } else {
                        echo "<p class='info'>Search proceeded with original code (unexpected for JudecatoriaROMANI)</p>\n";
                    }
                    
                } else {
                    echo "<p class='warning'>No institution metadata found - this may indicate the validation system is not fully integrated</p>\n";
                    $resultCount = is_array($results) ? count($results) : 0;
                    echo "<p class='info'>Search returned {$resultCount} results</p>\n";
                }
                
            } catch (Exception $e) {
                echo "<p class='error'>Search failed with exception: " . htmlspecialchars($e->getMessage()) . "</p>\n";
                echo "<p class='warning'>This indicates the fallback mechanism may not be working properly</p>\n";
            }
            
        } else {
            echo "<p class='warning'>DosarService class not found</p>\n";
        }
    } else {
        echo "<p class='warning'>DosarService file not found</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>Integration test failed: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

echo "</div>\n";

echo "<div class='test-section'>\n";
echo "<h2>Expected Behavior</h2>\n";
echo "<p class='info'>With our solution in place, the following should happen when searching with JudecatoriaROMANI:</p>\n";
echo "<ol>\n";
echo "<li>InstitutionCodeValidator detects that 'JudecatoriaROMANI' is problematic</li>\n";
echo "<li>If a mapping exists, it uses the mapped alternative code</li>\n";
echo "<li>If no mapping exists, the SOAP call is attempted anyway</li>\n";
echo "<li>If SOAP API rejects the code, fallback mechanism activates</li>\n";
echo "<li>Fallback performs search without institution filter</li>\n";
echo "<li>Results are returned with metadata indicating what happened</li>\n";
echo "<li>User sees appropriate warning message</li>\n";
echo "</ol>\n";

echo "<h3>Test Results Summary:</h3>\n";
if ($validation['fallback_needed'] || $validation['mapped']) {
    echo "<p class='success'>✓ Institution code is properly identified as problematic</p>\n";
} else {
    echo "<p class='warning'>⚠ Institution code not identified as problematic - may need to add to validator</p>\n";
}

if ($validation['warning']) {
    echo "<p class='success'>✓ Warning message is provided for user feedback</p>\n";
} else {
    echo "<p class='warning'>⚠ No warning message - user may not understand what happened</p>\n";
}

echo "</div>\n";

echo "<div class='test-section'>\n";
echo "<h2>Recommendations</h2>\n";

if (!$validation['mapped'] && !$validation['fallback_needed']) {
    echo "<p class='warning'>Recommendation: Add 'JudecatoriaROMANI' to the known problematic codes list</p>\n";
    echo "<p class='info'>You can do this by:</p>\n";
    echo "<ol>\n";
    echo "<li>Adding it to the \$knownProblematicCodes array in InstitutionCodeValidator</li>\n";
    echo "<li>Or adding a mapping to a valid alternative in \$invalidCodeMappings</li>\n";
    echo "</ol>\n";
}

if (!$alternative) {
    echo "<p class='info'>Consider adding a mapping for JudecatoriaROMANI to a valid tribunal code (e.g., TribunalulNEAMT)</p>\n";
}

echo "<p class='success'>The validation and fallback system is now in place to handle this and similar issues!</p>\n";
echo "</div>\n";
?>
